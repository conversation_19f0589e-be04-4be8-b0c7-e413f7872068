"""."""

import logging

import numpy as np
import pandas as pd
import torch
from torch import nn, optim

from src.strategies.base import BaseStrategy
from src.temp import (
    SignalType,
    StrategyConfig,
    TradingSignal,
)

logger = logging.getLogger(__name__)


class CNNStrategy(BaseStrategy):
    """Sample strategy using a simple CNN with PyTorch."""

    class SimpleCNN(nn.Module):
        """."""

        def __init__(self, input_channels: int = 1, num_classes: int = 3):
            super().__init__()
            self.conv1 = nn.Conv1d(input_channels, 8, kernel_size=3, padding=1)
            self.relu = nn.ReLU()
            self.pool = nn.AdaptiveMaxPool1d(1)
            self.fc = nn.Linear(8, num_classes)

        def forward(self, x: torch.Tensor) -> torch.Tensor:
            """."""
            x = self.conv1(x)
            x = self.relu(x)
            x = self.pool(x)
            x = x.view(x.size(0), -1)
            return self.fc(x)

    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.model = self.SimpleCNN()
        self.trained = False
        self.device = torch.device(
            "cuda" if torch.cuda.is_available() else "cpu",
        )
        self.model.to(self.device)

    def preprocess(self, df: pd.DataFrame, window: int = 20) -> np.ndarray:
        """Use close price window as input."""
        data: list[np.ndarray] = []
        for i in range(window, len(df)):
            window_data = df["close"].iloc[i - window : i].to_numpy()
            data.append(window_data)
        return np.array(data)

    def train_model(self, df: pd.DataFrame, window: int = 20) -> None:
        """Target: 0=SELL, 1=HOLD, 2=BUY based on next price movement."""
        X = self.preprocess(df, window)  # noqa: N806
        y = []
        for i in range(window, len(df) - 1):
            if df["close"].iloc[i + 1] > df["close"].iloc[i]:
                y.append(2)  # BUY
            elif df["close"].iloc[i + 1] < df["close"].iloc[i]:
                y.append(0)  # SELL
            else:
                y.append(1)  # HOLD
        X = torch.tensor(X, dtype=torch.float32).unsqueeze(1).to(self.device)  # noqa: N806
        y = torch.tensor(y, dtype=torch.long).to(self.device)
        optimizer = optim.Adam(self.model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        self.model.train()
        for _ in range(5):  # Keep epochs low for demo
            optimizer.zero_grad()
            outputs = self.model(X)
            loss = criterion(outputs, y)
            loss.backward()
            optimizer.step()

        self.trained = True

    def generate_signal(
        self,
        df: pd.DataFrame,
        index: int,
    ) -> TradingSignal | None:
        """."""
        window = 20
        if index < window:
            return None

        if not self.trained:
            self.train_model(df.iloc[:index])

        # Prepare input for prediction
        input_data = (
            df["close"]
            .iloc[index - window : index]
            .to_numpy()
            .astype(np.float32)
        )
        input_tensor = (
            torch.tensor(input_data, dtype=torch.float32)
            .unsqueeze(0)
            .unsqueeze(0)
            .to(self.device)
        )
        self.model.eval()
        with torch.no_grad():
            output = self.model(input_tensor)
            pred = torch.argmax(output, dim=1).item()
            conf = torch.softmax(output, dim=1).max().item()
        if pred == 2:
            signal = SignalType.BUY
            reason = "CNN predicts price up"
        elif pred == 0:
            signal = SignalType.SELL
            reason = "CNN predicts price down"
        else:
            signal = SignalType.HOLD
            reason = "CNN predicts hold"
        if conf < 0.5:  # noqa: PLR2004 TODO: refactor
            return None
        current = df.iloc[index]
        return TradingSignal(
            timestamp=current.name,
            signal=signal,
            confidence=conf,
            price=current["close"],
            indicators={
                "cnn_confidence": conf,
                # "bb_position": float,
                # "rsi": float,
                # "stoch_k": float,
                # "volume_ratio": float,
                # "trend": Trend,
            },
            reason=reason,
        )

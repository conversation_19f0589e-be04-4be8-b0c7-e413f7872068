"""."""

import logging

import pandas as pd
import streamlit as st
from dotenv import load_dotenv

from src.backtester import BacktestConfig, BackTestResults
from src.data_provider import PolygonDataProvider
from src.strategies.base import StrategyConfig
from src.utils import setup_logging

load_dotenv()

setup_logging()
logger = logging.getLogger(__name__)


class AppState:
    """Application state manager.

    Store and manage application state.
    """

    _symbol: str
    strategy_config: StrategyConfig
    backtest_config: BacktestConfig

    def __init__(self):
        self.data_provider = None
        self.current_data = None
        self.backtest_results = None

    @property
    def data_provider(self) -> PolygonDataProvider | None:
        """Return the data provider."""
        return st.session_state.data_provider

    @data_provider.setter
    def data_provider(self, reference: PolygonDataProvider | None) -> None:
        st.session_state.data_provider = reference

    @data_provider.deleter
    def data_provider(self) -> None:
        st.session_state.data_provider = None

    @property
    def current_data(self) -> pd.DataFrame | None:
        """Return the currently loaded data."""
        return st.session_state.current_data

    @current_data.setter
    def current_data(self, reference: pd.DataFrame | None) -> None:
        st.session_state.current_data = reference

    @current_data.deleter
    def current_data(self) -> None:
        st.session_state.current_data = None

    @property
    def backtest_results(self) -> BackTestResults | None:
        """Return the cached backtest results."""
        return st.session_state.backtest_results

    @backtest_results.setter
    def backtest_results(self, reference: BackTestResults | None) -> None:
        st.session_state.backtest_results = reference

    @backtest_results.deleter
    def backtest_results(self) -> None:
        st.session_state.backtest_results = None

    @property
    def symbol(self) -> str:
        """Return the currently selected symbol."""
        return self._symbol

    @symbol.setter
    def symbol(self, value: str) -> None:
        self._symbol = value

    @symbol.deleter
    def symbol(self) -> None:
        del self._symbol

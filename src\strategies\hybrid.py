"""."""

import logging

import pandas as pd

from src.strategies.base import BaseStrategy
from src.strategies.bollinger_bands_mean_reversion import (
    BollingerBandsMeanReversionStrategy,
)
from src.strategies.trend_following import TrendFollowingStrategy
from src.support_resistance import Trend
from src.temp import StrategyConfig, TradingSignal

logger = logging.getLogger(__name__)


class HybridStrategy(BaseStrategy):
    """Hybrid strategy combining multiple approaches."""

    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.mean_reversion = BollingerBandsMeanReversionStrategy(config)
        self.trend_following = TrendFollowingStrategy(config)

    def generate_signal(
        self,
        df: pd.DataFrame,
        index: int,
    ) -> TradingSignal | None:
        """Generate signal by combining multiple strategies."""
        try:
            # Get signals from both strategies
            mr_signal = self.mean_reversion.generate_signal(df, index)
            tf_signal = self.trend_following.generate_signal(df, index)

            # current = df.iloc[index]

            # Detect market regime
            trend = self.trend_analyzer.detect_trend(
                df["close"].iloc[: index + 1],
            )
            trend_strength = self.trend_analyzer.calculate_trend_strength(
                df["close"].iloc[: index + 1],
            )

            # Choose strategy based on market conditions
            if trend == Trend.SIDE or trend_strength < 0.3:
                # Use mean reversion in sideways markets
                return mr_signal
            # Use trend following in trending markets
            return tf_signal

        except Exception as e:
            logger.exception(f"Error generating hybrid signal: {e}")
            return None

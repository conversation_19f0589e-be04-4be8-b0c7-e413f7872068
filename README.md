# Broker-BackTester

This is a fresh project for a broker backtesting application. The app is in early development, so features and functionality will evolve over time.

## Getting Started

This project uses [uv](https://docs.astral.sh/uv/getting-started/installation/) for Python environment and dependency management. Please follow these steps to set up and run the app:

### 1. Install uv

**Recommended (Windows):**

Open PowerShell and run:

```
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**Recommended (Linux & macOS):**

Open a terminal and run:

```
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**Alternative methods:**

- With pipx: `pipx install uv`
- With pip: `pip install uv`

For more installation options and details, see the [uv installation docs](https://docs.astral.sh/uv/getting-started/installation/).

### 2. Install Dependencies (Recommended: uv sync)

From the project root directory, run:

```
uv sync
```

This will install all dependencies as specified in the lockfile, ensuring a reproducible environment. If a lockfile does not exist, uv will create one automatically.

### 3. Run the App

Start the app using:

```
streamlit run app.py
```

This will launch the app in your default web browser.

## What to Expect

- The application is in its initial stages, so the interface and features are subject to change.
- You may see basic UI elements and placeholders for future functionality.
- As development progresses, more features related to broker backtesting, data input, and strategy evaluation will be added.

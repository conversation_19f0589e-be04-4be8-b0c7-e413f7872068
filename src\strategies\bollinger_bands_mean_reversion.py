"""_summary_."""  # TODO: add docstring

import logging
from typing import cast

import pandas as pd

from src.strategies.base import BaseStrategy
from src.temp import Indicators, SignalType, TradingSignal

logger = logging.getLogger(__name__)


class BollingerBandsMeanReversionStrategy(BaseStrategy):
    """Mean reversion strategy using Bollinger Bands."""

    def extract_indicator_data(
        self,
        df: pd.DataFrame,
        index: int,
    ) -> Indicators:
        """."""
        current = df.iloc[index]

        bb_position = (current["close"] - current["bb_lower"]) / (
            current["bb_upper"] - current["bb_lower"]
        )
        trend = self.trend_analyzer.detect_trend(
            df["close"].iloc[: index + 1],
        )

        return {
            "bb_position": bb_position,
            "rsi": current["rsi"],
            "stoch_k": current["stoch_k"],
            "volume_ratio": current["volume_ratio"],
            "trend": trend,
        }

    def generate_signal(
        self,
        df: pd.DataFrame,
        index: int,
    ) -> TradingSignal | None:
        """Generate signal based on Bollinger Bands mean reversion."""
        try:
            current = df.iloc[index]
            indicators = self.extract_indicator_data(df, index)

            confidence = 0.0
            signal_type = SignalType.HOLD
            reason_parts: list[str] = []

            # Bollinger Bands signals
            if current["close"] <= current["bb_lower"]:
                # Potential buy signal (oversold)
                confidence += 0.3
                signal_type = SignalType.BUY
                reason_parts.append("Price at lower Bollinger Band")

                # RSI confirmation
                if current["rsi"] < self.config.rsi_oversold:
                    confidence += 0.2
                    reason_parts.append("RSI oversold")

                # Stochastic confirmation
                if current["stoch_k"] < self.config.stoch_oversold:
                    confidence += 0.2
                    reason_parts.append("Stochastic oversold")

                # Candlestick pattern confirmation
                if current.get("hammer", False):
                    confidence += 0.2
                    reason_parts.append("Hammer pattern")

            elif current["close"] >= current["bb_upper"]:
                # Potential sell signal (overbought)
                confidence += 0.3
                signal_type = SignalType.SELL
                reason_parts.append("Price at upper Bollinger Band")

                # RSI confirmation
                if current["rsi"] > self.config.rsi_overbought:
                    confidence += 0.2
                    reason_parts.append("RSI overbought")

                # Stochastic confirmation
                if current["stoch_k"] > self.config.stoch_overbought:
                    confidence += 0.2
                    reason_parts.append("Stochastic overbought")

                # Candlestick pattern confirmation
                if current.get("doji", False):
                    confidence += 0.1
                    reason_parts.append("Doji pattern")

            # Volume confirmation
            if (
                not self.config.require_volume_confirmation
                or current["volume_ratio"] > self.config.volume_threshold
            ):
                confidence += 0.1
                reason_parts.append("Volume confirmation")

            # Check minimum confidence threshold
            if confidence >= self.config.min_confidence:
                timestamp = cast("pd.Timestamp", current.name)
                return TradingSignal(
                    timestamp=timestamp,
                    signal=signal_type,
                    confidence=confidence,
                    price=current["close"],
                    indicators=indicators,
                    reason="; ".join(reason_parts),
                )

            return None

        except Exception as e:
            logger.exception(f"Error generating Bollinger Bands signal: {e}")
            return None

version: "3.9"

services:
  postgres:
    image: postgres:15
    container_name: trading_postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: trader
      POSTGRES_PASSWORD: traderpass
      POSTGRES_DB: marketdata
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  memcached:
    image: memcached:1.6
    container_name: trading_memcached
    restart: unless-stopped
    ports:
      - "11211:11211"

  streampuller:
    build:
      context: ./services/streampuller
    container_name: streampuller
    depends_on:
      - postgres
      - memcached
    environment:
      DATABASE_URL: ******************************************/marketdata
      CACHE_HOST: memcached
      PROVIDER_KEY: ${POLYGON_API_KEY}
    volumes:
      - ./services/streampuller:/app
    command: ["npm", "start"]

  decider:
    build:
      context: ./services/decider
    container_name: decider
    depends_on:
      - memcached
      - postgres
    environment:
      DATABASE_URL: ******************************************/marketdata
      CACHE_HOST: memcached
    volumes:
      - ./services/decider:/app
    command: ["python", "main.py"]

  orderproxy:
    build:
      context: ./services/orderproxy
    container_name: orderproxy
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    environment:
      BROKER_MODE: sandbox
      DATABASE_URL: ******************************************/marketdata
    volumes:
      - ./services/orderproxy:/app
    command: ["npm", "start"]

volumes:
  pgdata:

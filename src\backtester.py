"""Backtesting engine for trading strategies."""

import logging
from typing import cast

import pandas as pd

from src.backtest.results import BackTestResults
from src.strategies.base import BaseStrategy
from src.temp import (
    BacktestConfig,
    PositionSizing,
    SignalType,
    Snapshot,
    Trade,
    TradeSide,
    TradingSignal,
)

logger = logging.getLogger(__name__)


class BacktestEngine:
    """Main backtesting engine."""

    def __init__(self, config: BacktestConfig):
        self.config: BacktestConfig = config
        self.trades: list[Trade] = []
        self.current_positions: list[Trade] = []
        self.cash: float = config.initial_capital
        self.portfolio_value: float = config.initial_capital
        self.equity_curve: list[Snapshot] = []

    def reset(self) -> None:
        """Reset backtester state."""
        self.trades = []
        self.current_positions = []
        self.cash = self.config.initial_capital
        self.portfolio_value = self.config.initial_capital
        self.equity_curve = []

    def calculate_position_size(
        self,
        price: float,
        signal: TradingSignal,
    ) -> int:
        """Calculate position size based on configuration."""
        try:
            if self.config.position_sizing == PositionSizing.FIXED:
                # Fixed dollar amount
                if self.config.position_size > 1:
                    dollar_amount = self.config.position_size
                else:
                    dollar_amount = (
                        self.portfolio_value * self.config.position_size
                    )
            elif self.config.position_sizing == PositionSizing.PERCENT:
                # Percentage of portfolio
                dollar_amount = (
                    self.portfolio_value * self.config.position_size
                )
            elif self.config.position_sizing == PositionSizing.VOLATILITY:
                # Kelly criterion or volatility-based sizing (simplified)
                # volatility = signal.indicators.get('atr', price * 0.02) / price
                kelly_fraction = min(
                    0.25,
                    signal.confidence * 0.5,
                )  # Conservative Kelly
                dollar_amount = self.portfolio_value * kelly_fraction
            else:
                dollar_amount = (
                    self.portfolio_value * self.config.position_size
                )

            # Calculate number of shares
            shares = int(dollar_amount / price)

            # Ensure we don't exceed available cash
            max_shares = int(
                self.cash / (price * 1.01),
            )  # Account for slippage and commission
            shares = min(shares, max_shares)

            return max(0, shares)

        except Exception as e:
            logger.exception(f"Error calculating position size: {e}")
            return 0

    def calculate_transaction_costs(
        self,
        price: float,
        quantity: int,
    ) -> float:
        """Calculate transaction costs (commission + slippage)."""
        try:
            notional = price * quantity
            commission = max(
                self.config.commission_per_trade,
                notional * self.config.commission_pct,
            )
            slippage = notional * self.config.slippage_pct
            return commission + slippage

        except Exception as e:
            logger.exception(f"Error calculating transaction costs: {e}")
            return 0.0

    def check_stop_loss(self, trade: Trade, current_price: float) -> bool:
        """Check if stop loss should be triggered."""
        try:
            if not self.config.enable_stop_loss:
                return False

            if trade.side == TradeSide.LONG:
                stop_price = trade.entry_price * (
                    1 - self.config.stop_loss_pct
                )
                return current_price <= stop_price
            # short
            stop_price = trade.entry_price * (1 + self.config.stop_loss_pct)
            return current_price >= stop_price

        except Exception as e:
            logger.exception(f"Error checking stop loss: {e}")
            return False

    def check_take_profit(self, trade: Trade, current_price: float) -> bool:
        """Check if take profit should be triggered."""
        try:
            if not self.config.enable_take_profit:
                return False

            if trade.side == TradeSide.LONG:
                target_price = trade.entry_price * (
                    1 + self.config.take_profit_pct
                )
                return current_price >= target_price
            # short
            target_price = trade.entry_price * (
                1 - self.config.take_profit_pct
            )
            return current_price <= target_price

        except Exception as e:
            logger.exception(f"Error checking take profit: {e}")
            return False

    def update_trade_metrics(self, trade: Trade, current_price: float) -> None:
        """Update trade's adverse and favorable excursion metrics."""
        try:
            if trade.side == TradeSide.LONG:
                # For long positions
                unrealized_pnl_pct = (
                    current_price - trade.entry_price
                ) / trade.entry_price
                trade.max_favorable_excursion = max(
                    trade.max_favorable_excursion,
                    unrealized_pnl_pct,
                )
                trade.max_adverse_excursion = min(
                    trade.max_adverse_excursion,
                    unrealized_pnl_pct,
                )
            else:
                # For short positions
                unrealized_pnl_pct = (
                    trade.entry_price - current_price
                ) / trade.entry_price
                trade.max_favorable_excursion = max(
                    trade.max_favorable_excursion,
                    unrealized_pnl_pct,
                )
                trade.max_adverse_excursion = min(
                    trade.max_adverse_excursion,
                    unrealized_pnl_pct,
                )

        except Exception as e:
            logger.exception(f"Error updating trade metrics: {e}")

    def open_position(self, signal: TradingSignal) -> bool:
        """Open a new position based on signal."""
        try:
            # Check if we can open more positions
            if len(self.current_positions) >= self.config.max_positions:
                return False

            # Calculate position size
            quantity = self.calculate_position_size(signal.price, signal)
            if quantity <= 0:
                return False

            # Calculate transaction costs
            transaction_cost = self.calculate_transaction_costs(
                signal.price,
                quantity,
            )
            total_cost = signal.price * quantity + transaction_cost

            # Check if we have enough cash
            if total_cost > self.cash:
                return False

            # Create new trade
            side = (
                TradeSide.LONG
                if signal.signal == SignalType.BUY
                else TradeSide.SHORT
            )
            trade = Trade(
                entry_date=signal.timestamp,
                exit_date=None,
                entry_price=signal.price,
                exit_price=None,
                quantity=quantity,
                side=side,
                entry_signal=signal,
                exit_signal=None,
            )

            # Update cash and positions
            self.cash -= total_cost
            self.current_positions.append(trade)

            logger.info(
                f"Opened {side} position: {quantity} "
                f"shares at ${signal.price:.2f}",
            )
            return True

        except Exception as e:
            logger.exception(f"Error opening position: {e}")
            return False

    def close_position(
        self,
        trade: Trade,
        exit_signal: TradingSignal | None,
        current_price: float,
        current_date: pd.Timestamp,
        reason: str = "Signal",
    ) -> None:
        """Close an existing position."""
        try:
            # Calculate transaction costs
            transaction_cost = self.calculate_transaction_costs(
                current_price,
                trade.quantity,
            )

            # Calculate P&L
            if trade.side == TradeSide.LONG:
                gross_pnl = (
                    current_price - trade.entry_price
                ) * trade.quantity
            else:  # short
                gross_pnl = (
                    trade.entry_price - current_price
                ) * trade.quantity

            net_pnl = gross_pnl - transaction_cost
            pnl_pct = net_pnl / (trade.entry_price * trade.quantity)

            # Update trade record
            trade.exit_date = current_date
            trade.exit_price = current_price
            trade.exit_signal = exit_signal
            trade.pnl = net_pnl
            trade.pnl_pct = pnl_pct
            trade.duration = current_date - trade.entry_date

            # Update cash
            proceeds = current_price * trade.quantity - transaction_cost
            self.cash += proceeds

            # Move from current positions to completed trades
            self.current_positions.remove(trade)
            self.trades.append(trade)

            logger.info(
                f"Closed {trade.side} position: P&L "
                f"${net_pnl:.2f} ({pnl_pct:.2%}) - {reason}",
            )

        except Exception as e:
            logger.exception(f"Error closing position: {e}")

    def update_portfolio_value(self, current_price: float) -> None:
        """Update current portfolio value."""
        try:
            position_value = 0.0
            for trade in self.current_positions:
                if trade.side == TradeSide.LONG:
                    position_value += current_price * trade.quantity
                else:
                    # For short positions,
                    # we need to account for potential losses
                    position_value += (
                        2 * trade.entry_price - current_price
                    ) * trade.quantity

            self.portfolio_value = self.cash + position_value

        except Exception as e:
            logger.exception(f"Error updating portfolio value: {e}")

    def run_backtest(
        self,
        data: pd.DataFrame,
        strategy: BaseStrategy,
    ) -> BackTestResults | None:
        """Run complete backtest."""
        try:
            logger.info("Starting backtest...")
            self.reset()

            # Generate signals
            signals = strategy.backtest(data)
            signal_dict = {signal.timestamp: signal for signal in signals}

            # Process each day
            for date, row in data.iterrows():
                date = cast("pd.Timestamp", date)

                current_price = row["close"]

                # Update existing positions
                positions_to_close: list[tuple[Trade, str]] = []
                for trade in self.current_positions:
                    # Update trade metrics
                    self.update_trade_metrics(trade, current_price)

                    # Check stop loss and take profit
                    if self.check_stop_loss(trade, current_price):
                        positions_to_close.append((trade, "Stop Loss"))
                    elif self.check_take_profit(trade, current_price):
                        positions_to_close.append((trade, "Take Profit"))

                # Close positions that hit stops
                for trade, reason in positions_to_close:
                    self.close_position(
                        trade,
                        None,
                        current_price,
                        date,
                        reason,
                    )

                # Check for new signals
                if date in signal_dict:
                    signal = signal_dict[date]

                    if signal.signal is not SignalType.HOLD:
                        # Check if we should close existing positions first
                        opposite_signal = (
                            signal.signal == SignalType.SELL
                            and any(
                                t.side == TradeSide.LONG
                                for t in self.current_positions
                            )
                        ) or (
                            signal.signal == SignalType.BUY
                            and any(
                                t.side == TradeSide.SHORT
                                for t in self.current_positions
                            )
                        )

                        if opposite_signal:
                            # Close opposite positions
                            for trade in self.current_positions[:]:
                                if (
                                    signal.signal == SignalType.SELL
                                    and trade.side == TradeSide.LONG
                                ) or (
                                    signal.signal == SignalType.BUY
                                    and trade.side == TradeSide.SHORT
                                ):
                                    self.close_position(
                                        trade,
                                        signal,
                                        current_price,
                                        date,
                                        "Opposite Signal",
                                    )

                        # Open new position if we have capacity
                        if (
                            len(self.current_positions)
                            < self.config.max_positions
                        ):
                            self.open_position(signal)

                # Update portfolio value and equity curve
                self.update_portfolio_value(current_price)
                self.equity_curve.append(
                    {
                        "date": date,
                        "portfolio_value": self.portfolio_value,
                        "cash": self.cash,
                        "positions": len(self.current_positions),
                    },
                )

            # Close any remaining positions at the end
            final_date = data.index[-1]
            final_price = data.iloc[-1]["close"]
            for trade in self.current_positions[:]:
                self.close_position(
                    trade,
                    None,
                    final_price,
                    final_date,
                    "End of Backtest",
                )

            # Calculate final metrics
            results = self.generate_results()

            logger.info(
                "Backtest completed. Final portfolio value: "
                f"${self.portfolio_value:.2f}",
            )

            return results

        except Exception as e:
            logger.exception(f"Error running backtest: {e}")
            return None

    def generate_results(self) -> BackTestResults | None:
        """Generate comprehensive backtest results."""
        try:
            # Combine all results
            return BackTestResults(
                equity_curve=self.equity_curve,
                final_portfolio_value=self.portfolio_value,
                trades_list=self.trades,
                config=self.config,
            )

        except Exception as e:
            logger.exception(f"Error generating results: {e}")
            return None

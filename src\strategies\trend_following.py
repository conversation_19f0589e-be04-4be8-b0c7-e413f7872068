"""."""

import logging
from typing import cast

import pandas as pd

from src.strategies.base import BaseStrategy
from src.temp import SignalType, TradingSignal

logger = logging.getLogger(__name__)


class TrendFollowingStrategy(BaseStrategy):
    """Trend following strategy using moving averages and momentum."""

    def generate_signal(
        self,
        df: pd.DataFrame,
        index: int,
    ) -> TradingSignal | None:
        """Generate signal based on trend following."""
        try:
            current: pd.Series = df.iloc[index]
            prev: pd.Series = df.iloc[index - 1] if index > 0 else current

            indicators = {
                "ma_short": current["ma_short"],
                "ma_long": current["ma_long"],
                "macd": current["macd"],
                "macd_signal": current["macd_signal"],
                "rsi": current["rsi"],
                "volume_ratio": current["volume_ratio"],
            }

            confidence = 0.0
            signal_type = SignalType.HOLD
            reason_parts: list[str] = []

            # Moving average crossover
            ma_crossover_up = (
                current["ma_short"] > current["ma_long"]
                and prev["ma_short"] <= prev["ma_long"]
            )
            ma_crossover_down = (
                current["ma_short"] < current["ma_long"]
                and prev["ma_short"] >= prev["ma_long"]
            )

            if ma_crossover_up:
                confidence += 0.4
                signal_type = SignalType.BUY
                reason_parts.append("MA bullish crossover")

                # MACD confirmation
                if current["macd"] > current["macd_signal"]:
                    confidence += 0.2
                    reason_parts.append("MACD bullish")

                # RSI not overbought
                if current["rsi"] < 70:
                    confidence += 0.1
                    reason_parts.append("RSI not overbought")

                # Volume confirmation
                if current["volume_ratio"] > self.config.volume_threshold:
                    confidence += 0.2
                    reason_parts.append("High volume")

                # Price above moving averages
                if current["close"] > current["ma_short"]:
                    confidence += 0.1
                    reason_parts.append("Price above short MA")

            elif ma_crossover_down:
                confidence += 0.4
                signal_type = SignalType.SELL
                reason_parts.append("MA bearish crossover")

                # MACD confirmation
                if current["macd"] < current["macd_signal"]:
                    confidence += 0.2
                    reason_parts.append("MACD bearish")

                # RSI not oversold
                if current["rsi"] > 30:
                    confidence += 0.1
                    reason_parts.append("RSI not oversold")

                # Volume confirmation
                if current["volume_ratio"] > self.config.volume_threshold:
                    confidence += 0.2
                    reason_parts.append("High volume")

                # Price below moving averages
                if current["close"] < current["ma_short"]:
                    confidence += 0.1
                    reason_parts.append("Price below short MA")

            # Check minimum confidence threshold
            if confidence >= self.config.min_confidence:
                timestamp = cast("pd.Timestamp", current.name)
                return TradingSignal(
                    timestamp=timestamp,
                    signal=signal_type,
                    confidence=confidence,
                    price=current["close"],
                    indicators=indicators,
                    reason="; ".join(reason_parts),
                )

            return None

        except Exception as e:
            logger.exception(f"Error generating trend following signal: {e}")
            return None

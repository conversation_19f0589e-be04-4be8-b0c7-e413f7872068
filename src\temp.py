"""."""

from dataclasses import dataclass
from datetime import timedelta
from enum import Enum
from typing import TypedDict

import pandas as pd


class SignalType(Enum):
    """Trading signal types."""

    BUY = 1
    HOLD = 0
    SELL = -1


class Trend(Enum):
    """."""

    UP = "uptrend"
    SIDE = "sideways"
    DOWN = "downtrend"


class Indicators(TypedDict):
    """."""

    bb_position: float
    rsi: float
    stoch_k: float
    volume_ratio: float
    trend: Trend


@dataclass
class TradingSignal:
    """Trading signal data structure."""

    timestamp: pd.Timestamp
    signal: SignalType
    confidence: float
    price: float
    indicators: Indicators
    reason: str


class TradeSide(Enum):
    """_summary_."""

    LONG = "long"
    SHORT = "short"


@dataclass
class Trade:
    """Individual trade record."""

    entry_date: pd.Timestamp
    exit_date: pd.Timestamp | None
    entry_price: float
    exit_price: float | None
    quantity: int
    side: TradeSide
    entry_signal: TradingSignal
    exit_signal: TradingSignal | None
    pnl: float = 0.0
    pnl_pct: float = 0.0
    duration: timedelta | None = None
    max_adverse_excursion: float = 0.0
    max_favorable_excursion: float = 0.0


class PositionSizing(Enum):
    """Position sizing methods."""

    FIXED = "fixed"
    PERCENT = "percent"
    VOLATILITY = "volatility"


@dataclass
class BacktestConfig:
    """Backtesting configuration."""

    initial_capital: float = 100000
    commission_per_trade: float = 10.0
    commission_pct: float = 0.001  # 0.1%
    slippage_pct: float = 0.001  # 0.1%
    max_positions: int = 1
    position_sizing: PositionSizing = PositionSizing.FIXED
    position_size: float = 0.25  # 25% of capital or fixed amount
    enable_stop_loss: bool = True
    stop_loss_pct: float = 0.05  # 5%
    enable_take_profit: bool = True
    take_profit_pct: float = 0.10  # 10%
    trailing_stop: bool = False
    trailing_stop_pct: float = 0.03


class Snapshot(TypedDict):
    """Snapshot of portfolio state."""

    date: pd.Timestamp
    portfolio_value: float
    cash: float
    positions: int


@dataclass
class StrategyConfig:
    """Strategy configuration parameters."""

    strategy_type: str

    # Bollinger Bands
    bb_period: int = 20
    bb_std: float = 2.0

    # Stochastic Oscillator
    stoch_k_period: int = 14
    stoch_d_period: int = 3
    stoch_oversold: float = 20
    stoch_overbought: float = 80

    # RSI
    rsi_period: int = 14
    rsi_oversold: float = 30
    rsi_overbought: float = 70

    # Moving Averages
    ma_short_period: int = 10
    ma_long_period: int = 20

    # Volume
    volume_threshold: float = 1.5  # Multiple of average volume

    # Risk Management
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.10
    max_position_size: float = 0.25

    # Signal confirmation
    min_confidence: float = 0.6
    require_volume_confirmation: bool = True

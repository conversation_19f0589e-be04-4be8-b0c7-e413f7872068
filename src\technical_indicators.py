"""Technical indicators implementation for stock market analysis."""

import logging

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Technical indicators calculator."""

    @staticmethod
    def bollinger_bands(
        data: pd.Series,
        window: int = 20,
        num_std: float = 2.0,
    ) -> tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands.

        Args:
            data: Price series (typically close prices)
            window: Moving average window
            num_std: Number of standard deviations

        Returns:
            Tuple of (upper_band, middle_band, lower_band)

        """
        try:
            # Calculate moving average (middle band)
            middle_band = data.rolling(window=window).mean()

            # Calculate standard deviation
            std = data.rolling(window=window).std()

            # Calculate upper and lower bands
            band_width = std * num_std
            upper_band = middle_band + band_width
            lower_band = middle_band - band_width

            return upper_band, middle_band, lower_band

        except Exception as e:
            logger.exception(f"Error calculating Bollinger Bands: {e}")
            raise

    @staticmethod
    def stochastic_oscillator(
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        k_period: int = 14,
        d_period: int = 3,
    ) -> tuple[pd.Series, pd.Series]:
        """Calculate Stochastic Oscillator.

        Args:
            high: High prices
            low: Low prices
            close: Close prices
            k_period: %K period
            d_period: %D period (smoothing)

        Returns:
            Tuple of (%K, %D)

        """
        try:
            # Calculate %K
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()

            k_percent = 100 * (
                (close - lowest_low) / (highest_high - lowest_low)
            )

            # Calculate %D (smoothed %K)
            d_percent = k_percent.rolling(window=d_period).mean()

            return k_percent, d_percent

        except Exception as e:
            logger.exception(f"Error calculating Stochastic Oscillator: {e}")
            raise

    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index (RSI).

        Args:
            data: Price series
            window: RSI period

        Returns:
            RSI values

        """
        try:
            delta = data.diff()
            delta_mask = delta.to_numpy()
            gain_mask = delta_mask > 0
            loss_mask = delta_mask < 0
            gain = (delta.where(gain_mask, 0)).rolling(window=window).mean()
            loss = (-delta.where(loss_mask, 0)).rolling(window=window).mean()

            rs = gain / loss
            return 100 - (100 / (1 + rs))

        except Exception as e:
            logger.exception(f"Error calculating RSI: {e}")
            raise

    @staticmethod
    def moving_average(
        data: pd.Series,
        window: int,
        ma_type: str = "simple",
    ) -> pd.Series:
        """Calculate moving average.

        Args:
            data: Price series
            window: Moving average window
            ma_type: Type of moving average ('simple', 'exponential')

        Returns:
            Moving average values

        """
        try:
            if ma_type.lower() == "simple":
                return data.rolling(window=window).mean()
            if ma_type.lower() == "exponential":
                return data.ewm(span=window).mean()
            raise ValueError(f"Unsupported moving average type: {ma_type}")
        # TODO: change to empty array instead of raise

        except Exception as e:
            logger.exception(f"Error calculating moving average: {e}")
            raise

    @staticmethod
    def macd(
        data: pd.Series,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
    ) -> tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD (Moving Average Convergence Divergence).

        Args:
            data: Price series
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal line EMA period

        Returns:
            Tuple of (MACD line, Signal line, Histogram)

        """
        try:
            # Calculate exponential moving averages
            ema_fast = data.ewm(span=fast_period).mean()
            ema_slow = data.ewm(span=slow_period).mean()

            # Calculate MACD line
            macd_line = ema_fast - ema_slow

            # Calculate signal line
            signal_line = macd_line.ewm(span=signal_period).mean()

            # Calculate histogram
            histogram = macd_line - signal_line

            return macd_line, signal_line, histogram

        except Exception as e:
            logger.exception(f"Error calculating MACD: {e}")
            raise

    @staticmethod
    def volume_weighted_average_price(
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        volume: pd.Series,
    ) -> pd.Series:
        """Calculate Volume Weighted Average Price (VWAP).

        Args:
            high: High prices
            low: Low prices
            close: Close prices
            volume: Volume data

        Returns:
            VWAP values

        """
        try:
            # Calculate typical price
            typical_price = (high + low + close) / 3

            # Calculate cumulative volume and price-volume
            cum_volume = volume.cumsum()
            cum_price_volume = (typical_price * volume).cumsum()

            # Calculate VWAP
            return cum_price_volume / cum_volume

        except Exception as e:
            logger.exception(f"Error calculating VWAP: {e}")
            raise

    @staticmethod
    def volume_profile(
        close: pd.Series,
        volume: pd.Series,
        num_levels: int = 20,
    ) -> tuple[np.ndarray, np.ndarray]:
        """Calculate Volume Profile.

        Args:
            close: Close prices
            volume: Volume data
            num_levels: Number of price levels

        Returns:
            Tuple of (price_levels, volume_at_levels)

        """
        try:
            # Define price levels
            price_min = close.min()
            price_max = close.max()
            price_levels = np.linspace(price_min, price_max, num_levels)

            # Calculate volume at each price level
            volume_at_levels = np.zeros(num_levels - 1)

            for i in range(len(price_levels) - 1):
                mask = (close >= price_levels[i]) & (
                    close < price_levels[i + 1]
                )
                volume_at_levels[i] = volume[mask].sum()

            return price_levels[:-1], volume_at_levels

        except Exception as e:
            logger.exception(f"Error calculating Volume Profile: {e}")
            raise

    @staticmethod
    def atr(
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        window: int = 14,
    ) -> pd.Series:
        """Calculate Average True Range (ATR).

        Args:
            high: High prices
            low: Low prices
            close: Close prices
            window: ATR period

        Returns:
            ATR values

        """
        try:
            # Calculate True Range components
            tr1: pd.Series = high - low
            tr2: pd.Series = abs(high - close.shift(1))
            tr3: pd.Series = abs(low - close.shift(1))

            # True Range is the maximum of the three
            true_range = pd.concat([tr1, tr2, tr3], axis=1)
            mymax: pd.Series = true_range.max(axis=1, numeric_only=True)

            # Calculate ATR as smoothed average of True Range
            return mymax.rolling(window=window).mean()

        except Exception as e:
            logger.exception(f"Error calculating ATR: {e}")
            raise


class CandlestickPatterns:
    """Candlestick pattern recognition."""

    @staticmethod
    def is_hammer(
        open_price: float,
        high: float,
        low: float,
        close: float,
        threshold: float = 2.0,
    ) -> bool:
        """Detect hammer candlestick pattern.

        Args:
            open_price: Open price
            high: High price
            low: Low price
            close: Close price
            threshold: Body to shadow ratio threshold

        Returns:
            True if hammer pattern detected

        """
        try:
            body = abs(close - open_price)
            lower_shadow = min(open_price, close) - low
            upper_shadow = high - max(open_price, close)

            # Hammer conditions
            return (
                lower_shadow > threshold * body
                and upper_shadow < body * 0.5
                and body > 0
            )

        except Exception as e:
            logger.exception(f"Error detecting hammer pattern: {e}")
            return False

    @staticmethod
    def is_doji(
        open_price: float,
        close: float,
        high: float,
        low: float,
        threshold: float = 0.1,
    ) -> bool:
        """Detect doji candlestick pattern.

        Args:
            open_price: Open price
            close: Close price
            high: High price
            low: Low price
            threshold: Body size threshold as percentage of range

        Returns:
            True if doji pattern detected

        """
        try:
            body = abs(close - open_price)
            range_size = high - low

            if range_size == 0:
                return False

            # Doji condition: very small body relative to range
            return (body / range_size) < threshold

        except Exception as e:
            logger.exception(f"Error detecting doji pattern: {e}")
            return False

    @staticmethod
    def detect_patterns(df: pd.DataFrame) -> pd.DataFrame:
        """Detect various candlestick patterns in the data.

        Args:
            df: DataFrame with OHLC data

        Returns:
            DataFrame with pattern columns added

        """
        # TODO: Candle has to relate to the past and sometimes future in order to correctly detect patterns.  # noqa: E501
        try:
            df = df.copy()

            def temp(row: pd.Series) -> bool:
                return CandlestickPatterns.is_hammer(
                    row["open"],
                    row["high"],
                    row["low"],
                    row["close"],
                )

            def temp2(row: pd.Series) -> bool:
                return CandlestickPatterns.is_doji(
                    row["open"],
                    row["high"],
                    row["low"],
                    row["close"],
                )

            # Detect hammer patterns
            df["hammer"] = df.apply(temp, axis=1)

            # Detect doji patterns
            df["doji"] = df.apply(temp2, axis=1)

            return df

        except Exception as e:
            logger.exception(f"Error detecting candlestick patterns: {e}")
            return df

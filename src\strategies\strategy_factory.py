"""Trading strategy framework with modular strategy implementations."""

import logging

from src.strategies.base import BaseStrategy
from src.strategies.bollinger_bands_mean_reversion import (
    BollingerBandsMeanReversionStrategy,
)
from src.strategies.cnn_strategy import CNNStrategy
from src.strategies.hybrid import HybridStrategy
from src.strategies.trend_following import TrendFollowingStrategy
from src.temp import StrategyConfig

logger = logging.getLogger(__name__)


# Strategy factory
def create_strategy(config: StrategyConfig) -> BaseStrategy:
    # TODO: refactor. Now it only serves as validator.
    # It will not be needed with enum types.
    """Create strategy instances factory method.

    Args:
        strategy_type: Type of strategy to create
        config: Strategy configuration

    Returns:
        Strategy instance

    """
    strategies: dict[str, type[BaseStrategy]] = {
        "bollinger_mean_reversion": BollingerBandsMeanReversionStrategy,
        "trend_following": TrendFollowingStrategy,
        "hybrid": HybridStrategy,
        "cnn": CNNStrategy,
    }

    if config.strategy_type not in strategies:
        # TODO: dict[enum, refactor to generic class without 'type']]
        raise ValueError(f"Unknown strategy type: {config.strategy_type}")

    return strategies[config.strategy_type](config=config)

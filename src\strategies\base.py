"""TEMP."""

import abc
import logging

import pandas as pd

from src.support_resistance import SupportResistanceFinder, TrendAnalyzer
from src.technical_indicators import CandlestickPatterns, TechnicalIndicators
from src.temp import StrategyConfig, TradingSignal

logger = logging.getLogger(__name__)


class BaseStrategy(abc.ABC):
    """Base class for trading strategies."""

    def __init__(self, config: StrategyConfig):
        self.config = config
        self.indicators = TechnicalIndicators()
        self.patterns = CandlestickPatterns()
        self.sr_finder = SupportResistanceFinder()
        self.trend_analyzer = TrendAnalyzer()

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators."""
        try:
            df = df.copy()

            high_ser: pd.Series = df["high"]
            close_ser: pd.Series = df["close"]
            low_ser: pd.Series = df["low"]

            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                close_ser,
                self.config.bb_period,
                self.config.bb_std,
            )
            df["bb_upper"] = bb_upper
            df["bb_middle"] = bb_middle
            df["bb_lower"] = bb_lower

            # Stochastic Oscillator
            stoch_k, stoch_d = self.indicators.stochastic_oscillator(
                high_ser,
                low_ser,
                close_ser,
                self.config.stoch_k_period,
                self.config.stoch_d_period,
            )
            df["stoch_k"] = stoch_k
            df["stoch_d"] = stoch_d

            # RSI
            df["rsi"] = self.indicators.rsi(close_ser, self.config.rsi_period)

            # Moving Averages
            df["ma_short"] = self.indicators.moving_average(
                close_ser,
                self.config.ma_short_period,
            )
            df["ma_long"] = self.indicators.moving_average(
                close_ser,
                self.config.ma_long_period,
            )

            # MACD
            macd, signal, histogram = self.indicators.macd(close_ser)
            df["macd"] = macd
            df["macd_signal"] = signal
            df["macd_histogram"] = histogram

            # Volume indicators
            df["volume_ma"] = df["volume"].rolling(window=20).mean()
            df["volume_ratio"] = df["volume"] / df["volume_ma"]

            # ATR for volatility
            df["atr"] = self.indicators.atr(high_ser, low_ser, close_ser)

            # Candlestick patterns
            return self.patterns.detect_patterns(df)

        except Exception as e:
            logger.exception(f"Error calculating indicators: {e}")
            return df

    @abc.abstractmethod
    def generate_signal(
        self,
        df: pd.DataFrame,
        index: int,
    ) -> TradingSignal | None:
        """Generate trading signal - to be implemented by subclasses."""
        raise NotImplementedError(
            "Subclasses must implement generate_signal method",
        )

    def backtest(self, df: pd.DataFrame) -> list[TradingSignal]:
        """Run backtest on historical data."""
        try:
            # Calculate indicators
            df = self.calculate_indicators(df)

            signals: list[TradingSignal] = []
            for i in range(len(df)):
                if i < max(self.config.bb_period, self.config.ma_long_period):
                    continue  # Skip until we have enough data for indicators

                signal = self.generate_signal(df, i)
                if signal:
                    signals.append(signal)

            return signals

        except Exception as e:
            logger.exception(f"Error in backtest: {e}")
            return []

"""Utility functions for the backtesting application."""

import io
import logging

import pandas as pd

logger = logging.getLogger(__name__)


class DataUtils:
    """Utility functions for data manipulation."""

    @staticmethod
    def validate_ohlcv_data(df: pd.DataFrame) -> bool:
        """Validate OHLCV data format.

        Args:
            df: DataFrame to validate

        Returns:
            True if valid, False otherwise

        """
        try:
            required_columns = ["open", "high", "low", "close", "volume"]

            # Check if all required columns are present
            if not all(col in df.columns for col in required_columns):
                logger.exception(
                    f"Missing required columns. Expected: {required_columns}",
                )
                return False

            # Check for non-negative prices
            price_columns = ["open", "high", "low", "close"]
            if (df[price_columns] < 0).any().any():
                logger.exception("Found negative prices in data")
                return False

            # Check high >= low
            if (df["high"] < df["low"]).any():
                logger.exception("Found high < low in data")
                return False

            # Check OHLC consistency
            if (df["high"] < df[["open", "close"]].max(axis=1)).any():
                logger.exception("Found high < max(open, close)")
                return False

            if (df["low"] > df[["open", "close"]].min(axis=1)).any():
                logger.exception("Found low > min(open, close)")
                return False

            # Check for non-negative volume
            if (df["volume"] < 0).any():
                logger.exception("Found negative volume in data")
                return False

            # Check for missing values
            if df[required_columns].isna().any().any():
                logger.warning("Found missing values in data")

            return True

        except Exception as e:
            logger.exception(f"Error validating data: {e}")
            return False

    @staticmethod
    def clean_data(df: pd.DataFrame) -> pd.DataFrame:
        """Clean OHLCV data.

        Args:
            df: DataFrame to clean

        Returns:
            Cleaned DataFrame

        """
        try:
            df_clean = df.copy()

            # Remove rows with zero or negative prices
            price_columns = ["open", "high", "low", "close"]
            df_clean = df_clean[(df_clean[price_columns] > 0).all(axis=1)]

            # Remove rows with zero volume (optional, depends on strategy)
            # df_clean = df_clean[df_clean['volume'] > 0]

            # Fix OHLC inconsistencies
            df_clean["high"] = df_clean[["high", "open", "close"]].max(axis=1)
            df_clean["low"] = df_clean[["low", "open", "close"]].min(axis=1)

            # Forward fill missing values
            df_clean = df_clean.ffill()

            # Drop any remaining rows with NaN
            df_clean = df_clean.dropna()

            logger.info(f"Cleaned data: {len(df)} -> {len(df_clean)} rows")
            return df_clean

        except Exception as e:
            logger.exception(f"Error cleaning data: {e}")
            return df

    @staticmethod
    def resample_data(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Resample OHLCV data to different timeframe.

        Args:
            df: DataFrame with OHLCV data
            timeframe: Target timeframe ('1D', '1W', '1M', etc.)

        Returns:
            Resampled DataFrame

        """
        try:
            return (
                df.resample(timeframe)
                .agg(
                    {
                        "open": "first",
                        "high": "max",
                        "low": "min",
                        "close": "last",
                        "volume": "sum",
                    },
                )
                .dropna()
            )

        except Exception as e:
            logger.exception(f"Error resampling data: {e}")
            return df


class ExportUtils:
    """Utility functions for exporting results."""

    @staticmethod
    def export_to_csv(data: pd.DataFrame, filename: str) -> io.StringIO:
        """Export DataFrame to CSV string.

        Args:
            data: DataFrame to export
            filename: Filename for download

        Returns:
            StringIO object containing CSV data

        """
        try:
            output = io.StringIO()
            data.to_csv(output, index=True)
            output.seek(0)
            return output

        except Exception as e:
            logger.exception(f"Error exporting to CSV: {e}")
            return io.StringIO()

    @staticmethod
    def prepare_trade_export(trades_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare trades data for export.

        Args:
            trades_df: Trades DataFrame

        Returns:
            Formatted DataFrame for export

        """
        try:
            if len(trades_df) == 0:
                return pd.DataFrame()

            export_df = trades_df.copy()

            # Format dates
            if "entry_date" in export_df.columns:
                export_df["entry_date"] = export_df["entry_date"].dt.strftime(
                    "%Y-%m-%d %H:%M:%S",
                )
            if "exit_date" in export_df.columns:
                export_df["exit_date"] = export_df["exit_date"].dt.strftime(
                    "%Y-%m-%d %H:%M:%S",
                )

            # Format monetary values
            monetary_columns = ["entry_price", "exit_price", "pnl"]
            for col in monetary_columns:
                if col in export_df.columns:
                    export_df[col] = export_df[col].round(2)

            # Format percentages
            percentage_columns = [
                "pnl_pct",
                "max_adverse_excursion",
                "max_favorable_excursion",
            ]
            for col in percentage_columns:
                if col in export_df.columns:
                    export_df[col] = (export_df[col] * 100).round(2)

            return export_df

        except Exception as e:
            logger.exception(f"Error preparing trade export: {e}")
            return trades_df

    @staticmethod
    def prepare_equity_export(equity_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare equity curve data for export.

        Args:
            equity_df: Equity curve DataFrame

        Returns:
            Formatted DataFrame for export

        """
        try:
            if len(equity_df) == 0:
                return pd.DataFrame()

            export_df = equity_df.copy()

            # Reset index to include date column
            export_df = export_df.reset_index()

            # Format date
            if "date" in export_df.columns:
                export_df["date"] = export_df["date"].dt.strftime("%Y-%m-%d")

            # Round monetary values
            monetary_columns = ["portfolio_value", "cash"]
            for col in monetary_columns:
                if col in export_df.columns:
                    export_df[col] = export_df[col].round(2)

            return export_df

        except Exception as e:
            logger.exception(f"Error preparing equity export: {e}")
            return equity_df


def setup_logging(level: int = logging.INFO):
    """Setup logging configuration."""
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler()],
    )

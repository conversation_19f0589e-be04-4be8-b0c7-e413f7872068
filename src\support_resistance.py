"""Support and resistance level detection algorithms."""

import logging

import numpy as np
import pandas as pd
from scipy.signal import argrelextrema

from src.temp import Trend

logger = logging.getLogger(__name__)


class SupportResistanceFinder:
    """Support and resistance level detection."""

    def __init__(
        self,
        min_touches: int = 2,
        proximity_threshold: float = 0.02,
    ):
        """Initialize support/resistance finder.

        Args:
            min_touches: Minimum number of touches for a valid level
            proximity_threshold: Threshold for grouping
             nearby levels (as percentage)

        """
        self.min_touches = min_touches
        self.proximity_threshold = proximity_threshold

    def find_pivot_points(
        self,
        high: pd.Series,
        low: pd.Series,
        order: int = 5,
    ) -> tuple[list[tuple[float, float]], list[tuple[float, float]]]:
        """Find pivot highs and lows using local extrema.

        Args:
            high: High prices
            low: Low prices
            order: Number of points on each side to use for comparison

        Returns:
            Tuple of (pivot_highs, pivot_lows) as
             lists of (index, price) tuples

        """
        try:
            # Find local maxima (resistance levels)
            high_indices = argrelextrema(
                high.to_numpy(),
                np.greater,
                order=order,
            )[0]
            pivot_highs = [(high.index[i], high.iloc[i]) for i in high_indices]

            # Find local minima (support levels)
            low_indices = argrelextrema(low.to_numpy(), np.less, order=order)[
                0
            ]
            pivot_lows = [(low.index[i], low.iloc[i]) for i in low_indices]

            return pivot_highs, pivot_lows

        except Exception as e:
            logger.exception(f"Error finding pivot points: {e}")
            return [], []

    def group_nearby_levels(self, levels: list[float]) -> list[float]:
        """Group nearby price levels together.

        Args:
            levels: List of price levels

        Returns:
            List of grouped levels (average of nearby levels)

        """
        if not levels:
            return []

        levels = sorted(levels)
        grouped: list[float] = []
        current_group = [levels[0]]

        for level in levels[1:]:
            # Check if level is within proximity threshold of current group
            group_avg = np.mean(current_group)
            if abs(level - group_avg) / group_avg <= self.proximity_threshold:
                current_group.append(level)
            else:
                # Start new group
                grouped.append(np.mean(current_group, dtype=float))
                current_group = [level]

        # Add last group
        grouped.append(np.mean(current_group, dtype=float))

        return grouped

    def count_touches(
        self,
        price_data: pd.Series,
        level: float,
        tolerance: float = 0.01,
    ) -> int:
        """Count how many times price touched a level.

        Args:
            price_data: Price series (high or low)
            level: Price level to check
            tolerance: Touch tolerance as percentage

        Returns:
            Number of touches

        """
        try:
            touches = 0
            threshold = level * tolerance

            for price in price_data:
                if abs(price - level) <= threshold:
                    touches += 1

            return touches

        except Exception as e:
            logger.exception(f"Error counting touches: {e}")
            return 0

    def find_support_resistance_levels(
        self,
        df: pd.DataFrame,
        order: int = 5,
    ) -> dict[str, list[float]]:
        """Find significant support and resistance levels.

        Args:
            df: DataFrame with OHLC data
            order: Order for pivot point detection

        Returns:
            Dictionary with 'support' and 'resistance' levels

        """
        try:
            # Find pivot points
            pivot_highs, pivot_lows = self.find_pivot_points(
                df["high"],
                df["low"],
                order,
            )

            # Extract price levels
            resistance_levels = [price for _, price in pivot_highs]
            support_levels = [price for _, price in pivot_lows]

            # Group nearby levels
            resistance_levels = self.group_nearby_levels(resistance_levels)
            support_levels = self.group_nearby_levels(support_levels)

            # Filter levels by number of touches
            valid_resistance = []
            for level in resistance_levels:
                touches = self.count_touches(df["high"], level)
                if touches >= self.min_touches:
                    valid_resistance.append(level)

            valid_support = []
            for level in support_levels:
                touches = self.count_touches(df["low"], level)
                if touches >= self.min_touches:
                    valid_support.append(level)

            return {
                "resistance": sorted(valid_resistance, reverse=True),
                "support": sorted(valid_support),
            }

        except Exception as e:
            logger.exception(f"Error finding support/resistance levels: {e}")
            return {"resistance": [], "support": []}

    def get_current_levels(
        self,
        df: pd.DataFrame,
        current_price: float,
    ) -> dict[str, list[float]]:
        """Get support and resistance levels relevant to current price.

        Args:
            df: DataFrame with OHLC data
            current_price: Current price to filter levels

        Returns:
            Dictionary with relevant support and resistance levels

        """
        try:
            all_levels = self.find_support_resistance_levels(df)

            # Filter levels around current price
            nearby_resistance = [
                level
                for level in all_levels["resistance"]
                if level > current_price and level <= current_price * 1.1
            ]

            nearby_support = [
                level
                for level in all_levels["support"]
                if level < current_price and level >= current_price * 0.9
            ]

            return {
                "resistance": nearby_resistance[:3],  # Top 3 resistance levels
                "support": nearby_support[-3:],  # Top 3 support levels
            }

        except Exception as e:
            logger.exception(f"Error getting current levels: {e}")
            return {"resistance": [], "support": []}


class TrendAnalyzer:
    """Trend analysis and detection."""

    @staticmethod
    def detect_trend(prices: pd.Series, window: int = 20) -> Trend:
        """Detect overall trend direction.

        Args:
            prices: Price series
            window: Window for trend calculation

        Returns:
            Trend direction

        """
        try:
            if len(prices) < window:
                return Trend.SIDE

            # Calculate moving averages for trend detection
            short_ma = prices.rolling(window=window // 2).mean()
            long_ma = prices.rolling(window=window).mean()

            recent_short = short_ma.iloc[-5:].mean()
            recent_long = long_ma.iloc[-5:].mean()

            if recent_short > recent_long * 1.02:
                return Trend.UP
            if recent_short < recent_long * 0.98:
                return Trend.DOWN
            return Trend.SIDE

        except Exception as e:
            logger.exception(f"Error detecting trend: {e}")
            return Trend.SIDE

    @staticmethod
    def calculate_trend_strength(
        prices: pd.Series,
        window: int = 20,
    ) -> float:
        """Calculate trend strength (0-1 scale).

        Args:
            prices: Price series
            window: Window for calculation

        Returns:
            Trend strength value between 0 and 1

        """
        try:
            if len(prices) < window:
                return 0.0

            # Calculate price momentum
            price_change = (
                prices.iloc[-1] - prices.iloc[-window]
            ) / prices.iloc[-window]

            # Calculate volatility
            returns = prices.pct_change().dropna()
            volatility = returns.rolling(window=window).std().iloc[-1]

            # Trend strength is momentum adjusted for volatility
            if volatility == 0:
                return 0.0

            strength = abs(price_change) / (volatility * np.sqrt(window))
            return min(strength, 1.0)  # Cap at 1.0

        except Exception as e:
            logger.exception(f"Error calculating trend strength: {e}")
            return 0.0

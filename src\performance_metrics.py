"""Performance metrics calculation for backtesting results."""

import logging

import numpy as np
import pandas as pd
from numpy import floating
from pandas._typing import Scalar

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """Calculate various performance metrics for trading strategies."""

    @staticmethod
    def calculate_returns(equity_curve: pd.Series) -> pd.Series:
        """Calculate daily returns from equity curve."""
        try:
            return equity_curve.pct_change().dropna()
        except Exception as e:
            logger.exception(f"Error calculating returns: {e}")
            return pd.Series()

    @staticmethod
    def calculate_total_return(
        initial_capital: float,
        final_capital: float,
    ) -> float:
        """Calculate total return percentage."""
        try:
            return (final_capital - initial_capital) / initial_capital
        except Exception as e:
            logger.exception(f"Error calculating total return: {e}")
            return 0.0

    @staticmethod
    def calculate_annualized_return(total_return: float, days: int) -> float:
        """Calculate annualized return."""
        try:
            if days <= 0:
                return 0.0
            years = days / 365.25
            return (1 + total_return) ** (1 / years) - 1
        except Exception as e:
            logger.exception(f"Error calculating annualized return: {e}")
            return 0.0

    @staticmethod
    def calculate_volatility(
        returns: pd.Series,
        annualized: bool = True,
    ) -> float:
        """Calculate volatility (standard deviation of returns)."""
        try:
            if len(returns) == 0:
                return 0.0
            vol = returns.std()
            if annualized:
                vol *= np.sqrt(252)  # Assuming 252 trading days per year
            return vol
        except Exception as e:
            logger.exception(f"Error calculating volatility: {e}")
            return 0.0

    @staticmethod
    def calculate_sharpe_ratio(
        returns: pd.Series,
        risk_free_rate: float = 0.02,
    ) -> float:
        """Calculate Sharpe ratio."""
        try:
            if len(returns) == 0:
                return 0.0

            excess_returns = (
                returns
                - risk_free_rate / 252  # Assuming 252 trading days per year
            )  # Daily risk-free rate
            if excess_returns.std() == 0:
                return 0.0

            return (
                np.sqrt(252) * excess_returns.mean() / excess_returns.std()
            )  # Assuming 252 trading days per year
        except Exception as e:
            logger.exception(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    @staticmethod
    def calculate_sortino_ratio(
        returns: pd.Series,
        risk_free_rate: float = 0.02,
    ) -> float:
        """Calculate Sortino ratio (uses downside deviation)."""
        try:
            if len(returns) == 0:
                return 0.0

            excess_returns = (
                returns - risk_free_rate / 252
            )  # Assuming 252 trading days per year
            downside_returns = excess_returns[excess_returns < 0]

            if len(downside_returns) == 0 or downside_returns.std() == 0:
                return float("inf") if excess_returns.mean() > 0 else 0.0

            downside_deviation = downside_returns.std()
            return (
                np.sqrt(252) * excess_returns.mean() / downside_deviation
            )  # Assuming 252 trading days per year
        except Exception as e:
            logger.exception(f"Error calculating Sortino ratio: {e}")
            return 0.0

    @staticmethod
    def calculate_max_drawdown(
        equity_curve: pd.Series,
    ) -> dict[str, float]:
        """Calculate maximum drawdown and related metrics."""
        try:
            if len(equity_curve) == 0:
                return {
                    "max_drawdown": 0.0,
                    "max_drawdown_pct": 0.0,
                    "drawdown_duration": 0,
                }

            # Calculate running maximum
            running_max = equity_curve.expanding().max()

            # Calculate drawdown
            drawdown = equity_curve - running_max
            drawdown_pct = drawdown / running_max

            # Find maximum drawdown
            max_drawdown = drawdown.min()
            max_drawdown_pct = drawdown_pct.min()

            # Calculate drawdown duration
            # drawdown_duration = 0
            current_duration = 0
            max_duration = 0

            for dd in drawdown:
                if dd < 0:
                    current_duration += 1
                    max_duration = max(max_duration, current_duration)
                else:
                    current_duration = 0

            return {
                "max_drawdown": abs(max_drawdown),
                "max_drawdown_pct": abs(max_drawdown_pct),
                "drawdown_duration": max_duration,
            }
        except Exception as e:
            logger.exception(f"Error calculating max drawdown: {e}")
            return {
                "max_drawdown": 0.0,
                "max_drawdown_pct": 0.0,
                "drawdown_duration": 0,
            }

    @staticmethod
    def calculate_calmar_ratio(
        annualized_return: float,
        max_drawdown_pct: float,
    ) -> float:
        """Calculate Calmar ratio (annualized return / max drawdown)."""
        try:
            if max_drawdown_pct == 0:
                return float("inf") if annualized_return > 0 else 0.0
            return annualized_return / max_drawdown_pct
        except Exception as e:
            logger.exception(f"Error calculating Calmar ratio: {e}")
            return 0.0

    @staticmethod
    def calculate_win_rate(trades_df: pd.DataFrame) -> float:
        """Calculate win rate from trades."""
        try:
            if len(trades_df) == 0:
                return 0.0

            winning_trades = len(trades_df[trades_df["pnl"] > 0])
            return winning_trades / len(trades_df)
        except Exception as e:
            logger.exception(f"Error calculating win rate: {e}")
            return 0.0

    @staticmethod
    def calculate_profit_factor(trades_df: pd.DataFrame) -> float:
        """Calculate profit factor (gross profit / gross loss)."""
        try:
            if len(trades_df) == 0:
                return 0.0

            gross_profit = trades_df[trades_df["pnl"] > 0]["pnl"].sum()
            gross_loss = abs(trades_df[trades_df["pnl"] < 0]["pnl"].sum())

            if gross_loss == 0:
                return float("inf") if gross_profit > 0 else 0.0

            return gross_profit / gross_loss
        except Exception as e:
            logger.exception(f"Error calculating profit factor: {e}")
            return 0.0

    @staticmethod
    def calculate_average_trade(trades_df: pd.DataFrame) -> dict[str, float]:
        """Calculate average trade statistics."""
        try:
            if len(trades_df) == 0:
                return {
                    "avg_trade": 0.0,
                    "avg_win": 0.0,
                    "avg_loss": 0.0,
                    "avg_trade_pct": 0.0,
                    "avg_win_pct": 0.0,
                    "avg_loss_pct": 0.0,
                }

            winning_trades = trades_df[trades_df["pnl"] > 0]
            losing_trades = trades_df[trades_df["pnl"] < 0]

            return {
                "avg_trade": trades_df["pnl"].mean(),
                "avg_win": winning_trades["pnl"].mean()
                if len(winning_trades) > 0
                else 0.0,
                "avg_loss": losing_trades["pnl"].mean()
                if len(losing_trades) > 0
                else 0.0,
                "avg_trade_pct": trades_df["pnl_pct"].mean(),
                "avg_win_pct": winning_trades["pnl_pct"].mean()
                if len(winning_trades) > 0
                else 0.0,
                "avg_loss_pct": losing_trades["pnl_pct"].mean()
                if len(losing_trades) > 0
                else 0.0,
            }
        except Exception as e:
            logger.exception(f"Error calculating average trade: {e}")
            return {}

    @staticmethod
    def calculate_expectancy(trades_df: pd.DataFrame) -> float:
        """Calculate expectancy (average profit per trade)."""
        try:
            if len(trades_df) == 0:
                return 0.0

            win_rate = PerformanceMetrics.calculate_win_rate(trades_df)

            winning_trades = trades_df[trades_df["pnl"] > 0]
            losing_trades = trades_df[trades_df["pnl"] < 0]

            avg_win = (
                winning_trades["pnl"].mean()
                if len(winning_trades) > 0
                else 0.0
            )
            avg_loss = (
                losing_trades["pnl"].mean() if len(losing_trades) > 0 else 0.0
            )

            return (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
        except Exception as e:
            logger.exception(f"Error calculating expectancy: {e}")
            return 0.0

    @staticmethod
    def calculate_var(
        returns: pd.Series,
        confidence_level: float = 0.05,
    ) -> float | floating:
        """Calculate Value at Risk (VaR)."""
        try:
            if len(returns) == 0:
                return 0.0

            return np.percentile(returns, confidence_level * 100)
        except Exception as e:
            logger.exception(f"Error calculating VaR: {e}")
            return 0.0

    @staticmethod
    def calculate_cvar(
        returns: pd.Series,
        confidence_level: float = 0.05,
    ) -> float:
        """Calculate Conditional Value at Risk (CVaR)."""
        try:
            if len(returns) == 0:
                return 0.0

            var = PerformanceMetrics.calculate_var(returns, confidence_level)
            return returns[returns <= var].mean()
        except Exception as e:
            logger.exception(f"Error calculating CVaR: {e}")
            return 0.0

    @staticmethod
    def calculate_metrics(
        equity_curve: pd.Series,
        trades_df: pd.DataFrame,
        initial_capital: float,
        risk_free_rate: float = 0.02,
    ) -> dict[str, Scalar]:
        """Calculate comprehensive performance metrics."""
        try:
            if len(equity_curve) == 0:
                return {}

            # Basic calculations
            final_capital = equity_curve.iloc[-1]
            total_return = PerformanceMetrics.calculate_total_return(
                initial_capital,
                final_capital,
            )
            days = len(equity_curve)
            annualized_return = PerformanceMetrics.calculate_annualized_return(
                total_return,
                days,
            )

            # Returns and volatility
            returns = PerformanceMetrics.calculate_returns(equity_curve)
            volatility = PerformanceMetrics.calculate_volatility(returns)

            # Risk-adjusted returns
            sharpe_ratio = PerformanceMetrics.calculate_sharpe_ratio(
                returns,
                risk_free_rate,
            )
            sortino_ratio = PerformanceMetrics.calculate_sortino_ratio(
                returns,
                risk_free_rate,
            )

            # Drawdown metrics
            drawdown_metrics = PerformanceMetrics.calculate_max_drawdown(
                equity_curve,
            )
            calmar_ratio = PerformanceMetrics.calculate_calmar_ratio(
                annualized_return,
                drawdown_metrics["max_drawdown_pct"],
            )

            # Trade-based metrics
            win_rate = PerformanceMetrics.calculate_win_rate(trades_df)
            profit_factor = PerformanceMetrics.calculate_profit_factor(
                trades_df,
            )
            expectancy = PerformanceMetrics.calculate_expectancy(trades_df)
            avg_trade_metrics = PerformanceMetrics.calculate_average_trade(
                trades_df,
            )

            # Risk metrics
            var_5 = PerformanceMetrics.calculate_var(returns, 0.05)
            cvar_5 = PerformanceMetrics.calculate_cvar(returns, 0.05)

            # Additional statistics
            skewness = returns.skew() if len(returns) > 0 else 0.0
            kurtosis = returns.kurtosis() if len(returns) > 0 else 0.0

            # Compile all metrics
            metrics: dict[str, Scalar] = {
                # Return metrics
                "total_return": total_return,
                "annualized_return": annualized_return,
                "volatility": volatility,
                "sharpe_ratio": sharpe_ratio,
                "sortino_ratio": sortino_ratio,
                "calmar_ratio": calmar_ratio,
                # Drawdown metrics
                "max_drawdown": drawdown_metrics["max_drawdown"],
                "max_drawdown_pct": drawdown_metrics["max_drawdown_pct"],
                "drawdown_duration": drawdown_metrics["drawdown_duration"],
                # Trade metrics
                "total_trades": len(trades_df),
                "win_rate": win_rate,
                "profit_factor": profit_factor,
                "expectancy": expectancy,
                # Risk metrics
                "var_5": var_5,
                "cvar_5": cvar_5,
                "skewness": skewness,
                "kurtosis": kurtosis,
                # Portfolio metrics
                "initial_capital": initial_capital,
                "final_capital": final_capital,
                "days_traded": days,
            }

            # Add average trade metrics
            metrics.update(avg_trade_metrics)

            return metrics

        except Exception as e:
            logger.exception(f"Error calculating comprehensive metrics: {e}")
            return {}
